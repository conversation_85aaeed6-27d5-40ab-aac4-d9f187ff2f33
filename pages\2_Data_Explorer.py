import streamlit as st
import polars as pl
import pandas as pd 
import sys
import os
import pygwalker as pyg
from pygwalker.api.streamlit import StreamlitRenderer
from streamlit_navigation_bar import st_navbar
import time
from datetime import datetime
import logging
import traceback
import json
import numpy as np
import io
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import copy
import hashlib
import datacompy
import math
from great_tables import loc, style
try:
    import pyarrow as pa
    PYARROW_AVAILABLE = True
except ImportError:
    PYARROW_AVAILABLE = False
from app_core.utils.styling import apply_css
from app_core.config import (
    init_session_state, get_active_dataset, get_dataset_sample, 
    store_dataset, remove_dataset
)
st.set_page_config(
    layout='wide',
    page_title="Data Explorer - Visualize and analyze your data",
    page_icon="📊"
)

# Initialize session state
init_session_state()

# Apply CSS styling
apply_css()

# Set up logging for this page
logger = logging.getLogger(__name__)


# Function to highlight differences in Polars DataFrame
def highlight_polars_diff(pl_df: pl.DataFrame) -> pl.DataFrame:
    """
    Create a styled Polars DataFrame that highlights differences using Polars native styling.
    
    Args:
        pl_df: Polars DataFrame with _df1 and _df2 suffix columns
        
    Returns:
        pl.DataFrame with styling applied
    """
    base_fields = set(c[:-4] for c in pl_df.columns if c.endswith("_df1"))

    # Define a dictionary of style functions for all involved columns
    def make_highlight_fn(col1: str, col2: str):
        def style_fn(s: pl.Series, context: dict[str, pl.DataFrame]) -> pl.Series:
            other = context["frame"][col2 if s.name == col1 else col1]
            mask = (~s.is_null()) & (~other.is_null()) & (s != other)
            # Use different colors for df1 and df2 columns
            if s.name.endswith("_df1"):
                return mask.map_elements(lambda x: "background-color: #ffcccc" if x else None)
            else:
                return mask.map_elements(lambda x: "background-color: #ccffcc" if x else None)
        return style_fn

    # Map style functions to column selectors
    style_map = {
        col: make_highlight_fn(f"{base}_df1", f"{base}_df2")
        for base in base_fields
        for col in (f"{base}_df1", f"{base}_df2")
    }

    # Apply styling
    styled_df = pl_df.style(frame=style_map).hide_column_dtype()
    return styled_df

def generate_custom_report(compare_obj, df1_pandas, df2_pandas):
    """Generate a custom comparison report for two datasets using Polars for efficiency
    
    Args:
        compare_obj: datacompy.Compare object with comparison results
        df1_pandas: First DataFrame (original) in Pandas format
        df2_pandas: Second DataFrame (new) in Pandas format
        
    Returns:
        tuple: Summary stats, unique rows in each dataset, mismatched rows, and column info
    """
    # Convert to Polars for more efficient processing
    df1 = pl.from_pandas(df1_pandas) if not isinstance(df1_pandas, pl.DataFrame) else df1_pandas
    df2 = pl.from_pandas(df2_pandas) if not isinstance(df2_pandas, pl.DataFrame) else df2_pandas
    
    # Basic statistics
    df1_rows = df1.height
    df2_rows = df2.height
    
    # Column comparison
    df1_cols = set(df1.columns)
    df2_cols = set(df2.columns)
    common_cols = df1_cols.intersection(df2_cols)
    only_df1_cols = df1_cols - df2_cols
    only_df2_cols = df2_cols - df1_cols
    
    # Get mismatched data from datacompy
    only_df1_pandas = compare_obj.df1_unq_rows
    only_df2_pandas = compare_obj.df2_unq_rows
    mismatch_df_pandas = compare_obj.all_mismatch()
    
    # Convert to Polars
    only_df1 = pl.from_pandas(only_df1_pandas) if hasattr(only_df1_pandas, 'shape') else pl.DataFrame()
    only_df2 = pl.from_pandas(only_df2_pandas) if hasattr(only_df2_pandas, 'shape') else pl.DataFrame()
    mismatch_df = pl.from_pandas(mismatch_df_pandas) if hasattr(mismatch_df_pandas, 'shape') else pl.DataFrame()
    
    # Generate summary statistics
    summary = {
        'df1_shape': (df1.height, df1.width),
        'df2_shape': (df2.height, df2.width),
        'common_columns': len(common_cols),
        'columns_only_df1': list(only_df1_cols) if only_df1_cols else [],
        'columns_only_df2': list(only_df2_cols) if only_df2_cols else [],
        'rows_only_df1': only_df1.height if only_df1.height > 0 else 0,
        'rows_only_df2': only_df2.height if only_df2.height > 0 else 0,
        'mismatched_rows': mismatch_df.height if mismatch_df.height > 0 else 0,
        'matching_rows': compare_obj.intersect_rows.shape[0] if hasattr(compare_obj.intersect_rows, 'shape') else 0
    }
    
    return summary, only_df1, only_df2, mismatch_df, common_cols, only_df1_cols, only_df2_cols

@st.cache_resource
def get_pygwalker_renderer(viz_df, walker_key):
    """Initialize and return the PyGWalker Streamlit renderer."""
    return StreamlitRenderer(viz_df, gid=walker_key, height=1600)

def show_visual_explorer():
    """Display the Visual Explorer page content"""
    lazy_dataset = get_active_dataset()
    viz_df = lazy_dataset.get_full_data()
    # Load data for visualization
    try:
        st.info(f"Using full dataset with {viz_df.shape[0]:,} rows")
        # Initialize PyGWalker renderer
        if viz_df is not None and not viz_df.empty:
            walker_key = f"walker_{st.session_state.active_dataset}_{hash(str(viz_df.columns.tolist()))}"
            # Create PyGWalker renderer with larger height
            renderer = get_pygwalker_renderer(viz_df, walker_key)
            # Render the interactive visualization with a larger height
            renderer.explorer(default_tab="data")
        else:
            st.warning("No data available for visualization")
    except Exception as e:
        st.error(f"Error loading data for visualization: {str(e)}")
        logger.error(f"PyGWalker visualization error: {str(e)}")
        st.dataframe(viz_df, use_container_width=True)

def show_data_profiling():
    """Display the Data Profiling page content"""
    lazy_dataset = get_active_dataset()
    
    # Profiling Configuration Section
    st.markdown("""
        <div style="background: white; padding: 1.5rem; border-radius: 10px; 
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 2rem; border-left: 4px solid #2196F3;">
            <h3 style="margin: 0 0 1rem 0; color: #1976D2;">⚙️ Profiling Configuration</h3>
        </div>
        """, unsafe_allow_html=True)
            
    # Enhanced profiling options
    col_left, col_right = st.columns([5, 1])
            
    with col_left:
        st.markdown("**Select Analysis Mode:**")
                
        # Custom styled radio buttons using columns
        mode_col1, mode_col2 = st.columns(2)
        
        with mode_col1:
            minimal_selected = st.button(
                "🔬 Quick Analysis", 
                help="Fast profiling with essential statistics and basic visualizations",
                use_container_width=True,
                key="minimal_mode"
            )
        
        with mode_col2:
            detailed_selected = st.button(
                "🔍 Deep Analysis", 
                help="Comprehensive profiling with advanced statistics, correlations, and detailed visualizations",
                use_container_width=True,
                key="detailed_mode"
            )
                
        # Store the selected mode in session state
        if minimal_selected:
            st.session_state.profiling_mode = "minimal"
        elif detailed_selected:
            st.session_state.profiling_mode = "detailed"
        
        # Default to minimal if no selection
        if 'profiling_mode' not in st.session_state:
            st.session_state.profiling_mode = "minimal"
        
        # Show selected mode
        mode_display = "🔬 Quick Analysis" if st.session_state.profiling_mode == "minimal" else "🔍 Deep Analysis"
        st.info(f"**Selected Mode:** {mode_display}")

            
        # Generate Report Button with enhanced styling
        col_center = st.columns([1, 2, 1])[1]
        with col_center:
            generate_report = st.button(
                "🚀 Generate Profiling Report",
                help=f"Generate a comprehensive {st.session_state.profiling_mode} analysis report",
                use_container_width=True,
                type="primary"
            )
            
            if generate_report:
                # Enhanced loading experience
                progress_container = st.container()
                
                with progress_container:
                    # Progress indicators
                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    
                    try:
                        # Step 1: Data loading
                        status_text.text("📥 Loading dataset...")
                        progress_bar.progress(20)
                        
                        df_for_profiling = lazy_dataset.get_full_data()
                        
                        # Step 2: Data preprocessing
                        status_text.text("🔧 Preprocessing data...")
                        progress_bar.progress(40)
                        
                        # Step 3: Generating profile
                        status_text.text("📊 Generating analysis report...")
                        progress_bar.progress(60)
                        
                        # Enhanced profile configuration
                        profile_config = {
                            "title": f"📈 {st.session_state.active_dataset} - Data Profile",
                            "minimal": (st.session_state.profiling_mode == "minimal"),
                            "explorative": (st.session_state.profiling_mode == "detailed"),
                            "dark_mode": False,
                            "orange_mode": False
                        }
                        
                        if st.session_state.profiling_mode == "detailed":
                            profile_config.update({
                                "correlations": {"auto": {"calculate": True}},
                                "missing_diagrams": {"auto": {"calculate": True}},
                                "duplicates": {"auto": {"calculate": True}},
                                "interactions": {"auto": {"calculate": True}}
                            })
                        
                        profile = ProfileReport(df_for_profiling, **profile_config)
                        
                        # Step 4: Rendering
                        status_text.text("🎨 Rendering report...")
                        progress_bar.progress(80)
                        
                        # Clear progress indicators
                        progress_bar.progress(100)
                        status_text.text("✅ Report generated successfully!")
                        
                        # Success message with animation
                        st.balloons()
                        
                        # Clear progress after a moment
                        time.sleep(1)
                        progress_container.empty()
                        
                        # Display the report in an enhanced container
                        st.markdown("""
                        <div style="background: white; padding: 1rem; border-radius: 10px; 
                                   box-shadow: 0 4px 20px rgba(0,0,0,0.1); margin: 2rem 0;">
                            <h3 style="margin: 0 0 1rem 0; color: #1976D2; text-align: center;">
                                📊 Interactive Data Profile Report
                            </h3>
                        </div>
                        """, unsafe_allow_html=True)
                        
                        # Render the profile report
                        st.components.v1.html(profile.to_html(), height=1200, scrolling=True)
                        
                        # Add download option
                        st.markdown("<br>", unsafe_allow_html=True)
                        col_download = st.columns([1, 1, 1])[1]
                        with col_download:
                            if st.button("💾 Save Report as HTML", use_container_width=True):
                                html_report = profile.to_html()
                                st.download_button(
                                    label="📥 Download HTML Report",
                                    data=html_report,
                                    file_name=f"{st.session_state.active_dataset}_profile_report.html",
                                    mime="text/html",
                                    use_container_width=True
                                )
                        
                    except Exception as e:
                        # Enhanced error handling
                        progress_container.empty()
                        
                        st.markdown("""
                        <div style="background: #ffebee; padding: 1.5rem; border-radius: 10px; 
                                   border-left: 4px solid #f44336; margin: 1rem 0;">
                            <h4 style="color: #c62828; margin: 0 0 0.5rem 0;">⚠️ Profiling Error</h4>
                            <p style="margin: 0; color: #666;">Unable to generate the full profiling report.</p>
                        </div>
                        """, unsafe_allow_html=True)
                        
                        st.error(f"**Error details:** {str(e)}")
                        logger.error(f"Profiling error: {str(e)}")
                        
                        # Enhanced fallback summary
                        st.markdown("""
                        <div style="background: white; padding: 1.5rem; border-radius: 10px; 
                                   box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin: 2rem 0; border-left: 4px solid #FF9800;">
                            <h3 style="margin: 0 0 1rem 0; color: #F57C00;">📋 Alternative Data Summary</h3>
                        </div>
                        """, unsafe_allow_html=True)
                        
                        col_info = lazy_dataset.get_column_info()
                        
                        # Enhanced metrics layout
                        metric_col1, metric_col2, metric_col3, metric_col4 = st.columns(4)
                        
                        with metric_col1:
                            st.metric(
                                label="📏 Total Rows",
                                value=f"{lazy_dataset.shape[0]:,}",
                                help="Number of records in the dataset"
                            )
                        
                        with metric_col2:
                            st.metric(
                                label="📋 Total Columns", 
                                value=lazy_dataset.shape[1],
                                help="Number of features/variables"
                            )
                        
                        with metric_col3:
                            st.metric(
                                label="🔢 Numeric Columns",
                                value=len(col_info['numeric_columns']),
                                help="Columns containing numerical data"
                            )
                        
                        with metric_col4:
                            total_nulls = sum(col_info['null_counts'].values())
                            st.metric(
                                label="❌ Missing Values",
                                value=f"{total_nulls:,}",
                                help="Total number of missing/null values"
                            )
                        
                        # Additional column type breakdown
                        st.markdown("<br>", unsafe_allow_html=True)
                        type_col1, type_col2, type_col3 = st.columns(3)
                        
                        with type_col1:
                            st.metric(
                                label="📝 Text Columns",
                                value=len(col_info['string_columns']),
                                help="Columns containing text/categorical data"
                            )
                        
                        with type_col2:
                            st.metric(
                                label="📅 Date Columns",
                                value=len(col_info['date_columns']),
                                help="Columns containing date/time data"
                            )
                        
                        with type_col3:
                            other_cols = lazy_dataset.shape[1] - len(col_info['numeric_columns']) - len(col_info['string_columns']) - len(col_info['date_columns'])
                            st.metric(
                                label="🔧 Other Types",
                                value=max(0, other_cols),
                                help="Columns with other data types"
                            )
            
    with col_right:
        st.markdown("**Analysis Features:**")
        if st.session_state.profiling_mode == "minimal":
            st.markdown("""
            ✅ Basic statistics  
            ✅ Missing values  
            ✅ Data types  
            ✅ Sample data  
            ⚡ Fast execution
            """)
        else:
            st.markdown("""
            ✅ Advanced statistics  
            ✅ Correlation analysis  
            ✅ Distribution plots  
            ✅ Outlier detection  
            ✅ Duplicate analysis  
            🔍 Detailed insights
            """)
        
        st.markdown("<br>", unsafe_allow_html=True)

def show_data_quality():
    """Display the Data Quality page content"""
    # Data Quality tab implementation using lazy dataset
    lazy_dataset = get_active_dataset()
    
    if lazy_dataset:
          # Load data for quality checks
        @st.cache_data
        def load_quality_data(dataset_name: str) -> pd.DataFrame:
            """Load data for quality checks with caching"""
            lazy_ds = st.session_state.lazy_datasets[dataset_name]
            # Always use full dataset for quality checks
            return lazy_ds.get_full_data()
        
        # Load data for quality checks
        try:
            quality_df = load_quality_data(st.session_state.active_dataset)
        except Exception as e:
            st.error(f"Error loading data for quality checks: {str(e)}")
            st.stop()
        
        # Show dataset info
        st.subheader("Dataset Information")
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Rows", f"{lazy_dataset.shape[0]:,}")
        with col2:
            st.metric("Columns", lazy_dataset.shape[1])
        with col3:
            total_nulls = quality_df.isnull().sum().sum()
            st.metric("Missing Values", f"{total_nulls:,}")
        
        # Create subtabs for different data quality operations
        quality_tab1, quality_tab2, quality_tab3 = st.tabs(["📋 Define Expectations", "🔍 Run Validations", "📊 Quality Reports"])
        
        # ... (rest of the data quality content - I'll continue in the next part)

def show_dataset_comparison():
    """Display the Dataset Comparison page content"""
    st.subheader("Compare Datasets")
    
    # Only show comparison UI if we have at least 2 datasets
    if len(st.session_state.lazy_datasets) < 2:
        st.warning("You need at least two datasets to perform a comparison. Please upload another dataset.")
    else:
        # Dataset selection
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("Dataset 1")
            dataset1 = st.selectbox(
                "Select first dataset:",
                options=list(st.session_state.lazy_datasets.keys()),
                key="compare_dataset1"
            )
        
        with col2:
            st.subheader("Dataset 2")
            # Filter out the first selected dataset
            remaining_datasets = [ds for ds in st.session_state.lazy_datasets.keys() if ds != dataset1]
            dataset2 = st.selectbox(
                "Select second dataset",
                options=remaining_datasets,
                key="compare_dataset2"
            )
        
        # Join column selection
        if dataset1 and dataset2:
            # Get column lists for both datasets
            # Use get_column_info() instead of get_column_names()
            df1_cols = list(st.session_state.lazy_datasets[dataset1].get_column_info()['dtypes'].keys())
            df2_cols = list(st.session_state.lazy_datasets[dataset2].get_column_info()['dtypes'].keys())
            
            # Find common columns as potential join keys
            common_cols = list(set(df1_cols).intersection(set(df2_cols)))
            
            # Let user select join columns
            join_cols = st.multiselect(
                "Select columns to join on:",
                options=common_cols,
                default=common_cols[:1] if common_cols else []
            )

            # Let user select columns to ignore
            ignore_cols = st.multiselect(
                "Select columns to ignore in comparison (optional):",
                options=common_cols,
                default=[]
            )
            
            if join_cols:
                if st.button("Run Comparison", type="primary"):
                    # Comparison logic will be added here
                    st.info("Comparison functionality will be implemented here")
            else:
                st.info("Please select at least one column to join on")

# Initialize Great Expectations related session state variables if not already initialized
if 'expectation_suites' not in st.session_state:
    st.session_state.expectation_suites = {}

if 'validation_results' not in st.session_state:
    st.session_state.validation_results = {}

# Apply centralized CSS
apply_css()

# Hide the Streamlit deprecation warning alert for st.experimental_user
st.markdown("""
<style>
  /* hide any warning-alert container */
  div[role="alert"][data-baseweb="notification"] {
    display: none !important;
  }
  
  /* PyGWalker iframe fixes */
  iframe[title*="pygwalker"], 
  iframe[src*="pygwalker"],
  iframe[data-testid*="stIframe"] {
    height: 800px !important;
  }
  
  /* Ensure PyGWalker container has proper height */
  .stIframe, 
  .streamlit-expanderHeader,
  div[data-testid="stIframe"] {
    height: 1600px !important;
    min-height: 1600px !important;
    overflow: visible !important;
  }
  
  /* Force proper scrolling for PyGWalker */
  .pygwalker-container,
  .pygwalker-wrapper {
    height: 1600px !important;
    overflow: auto !important;
    overflow-x: auto !important;
    overflow-y: auto !important;
  }
</style>
""", unsafe_allow_html=True)

# Import ydata_profiling and streamlit component after page config
try:
    import polars as pl
    # Make sure matplotlib is available
    import matplotlib.pyplot as plt

    # Use ydata_profiling for profiling reports
    from ydata_profiling import ProfileReport
    PANDAS_PROFILING_AVAILABLE = True
except ImportError:
    PANDAS_PROFILING_AVAILABLE = False
    st.warning("Pandas Profiling is not available. Please install it with: pip install ydata-profiling matplotlib")

# Session state variables for PyGWalker are now initialized in config.py
# Initialize and check PyGWalker configuration

# Create a sidebar with options
with st.sidebar:
    st.header("Explorer Options")

    # Add dataset selector if multiple datasets exist
    if len(st.session_state.lazy_datasets) > 0:
        st.subheader("Select Dataset")

        # Create a radio button for dataset selection
        dataset_options = list(st.session_state.lazy_datasets.keys())
        selected_dataset = st.radio(
            "Choose a dataset to explore:",
            dataset_options,
            index=dataset_options.index(st.session_state.active_dataset) if st.session_state.active_dataset in dataset_options else 0
        )

        # Update the active dataset if changed
        if selected_dataset != st.session_state.active_dataset:
            st.session_state.active_dataset = selected_dataset
            st.rerun()
        
        # Show dataset info
        if st.session_state.active_dataset:
            lazy_dataset = st.session_state.lazy_datasets[st.session_state.active_dataset]
            st.info(f"""
            **Dataset Info:**
            - Rows: {lazy_dataset.shape[0]:,}
            - Columns: {lazy_dataset.shape[1]}
            - Memory: {lazy_dataset.memory_usage_mb:.1f} MB
            """)

# Main content - check if we have data
if not st.session_state.has_data or not st.session_state.lazy_datasets:
    # Enhanced empty state presentation

    
    # Create centered layout with columns
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        # Main heading with icon
        st.markdown("""
        <div style="text-align: center;">
            <h1 style="color: #1f77b4;">
                📊 Data Explorer
            </h1>
            <p style="font-size: 1.2rem; color: #666; margin-bottom: 2rem;">
                Discover insights in your data with interactive visualizations
            </p>
        </div>
        """, unsafe_allow_html=True)
        
        # Info box about what's available
        st.info("""
        🔍 **What you can do with Data Explorer:**
        
        • **Visual Explorer**: Interactive charts and graphs with PyGWalker
        • **Data Profiling**: Comprehensive statistical analysis 
        • **Data Quality**: Identify missing values, duplicates, and anomalies
        • **Dataset Comparison**: Switch between multiple uploaded datasets
        """)
        
        # Getting started section
        st.markdown("""
        ### 🚀 Get Started
        
        To start exploring your data, you'll need to upload at least one dataset first.
        """)
        
        # Action buttons
        st.markdown("<br>", unsafe_allow_html=True)
        
        # Create two columns for buttons
        btn_col1, btn_col2 = st.columns(2)
        
        with btn_col1:
            if st.button("📤 Upload Your Data", type="primary", use_container_width=True):
                st.switch_page("pages/1_Upload_Data.py")
        
        with btn_col2:
            if st.button("📥 Import from Oracle", type="primary", use_container_width=True):
                st.switch_page("pages/3_Database_Connections.py")
      # Add some spacing
    st.markdown("<br><br>", unsafe_allow_html=True)
    
else:
    # Add page header when datasets are available
    
    st.title("📊 Data Explorer - Visualize and Analyze Your Data")
    
    # Navigation bar configuration
    pages = ["📊 Visual Explorer", "📝 Data Profiling", "✅ Data Quality", "🔄 Dataset Comparison"]
    styles = {
        "nav": {
            "background-color": "#1f77b4",
            "justify-content": "center",
        },
        "span": {
            "color": "white",
            "padding": "14px",
        },
        "active": {
            "background-color": "white",
            "color": "var(--text-color)",
            "font-weight": "normal",
            "padding": "14px",
        }
    }
    options = {
        "show_menu": False,
        "show_sidebar": False,
    }
    
    # Create navigation bar
    selected_page = st_navbar(
        pages,
        styles=styles,
        options=options,
    )
    
    # Default to first page if none selected
    if not selected_page:
        selected_page = pages[0]
    
    # Display the selected page content
    if selected_page == "📊 Visual Explorer":
        show_visual_explorer()
    elif selected_page == "📝 Data Profiling":
        show_data_profiling()
    elif selected_page == "✅ Data Quality":
        show_data_quality()
    elif selected_page == "🔄 Dataset Comparison":
        show_dataset_comparison()
        viz_df = lazy_dataset.get_full_data()
        # Load data for visualization
        try:
            st.info(f"Using full dataset with {viz_df.shape[0]:,} rows")
            # Initialize PyGWalker renderer
            if viz_df is not None and not viz_df.empty:
                walker_key = f"walker_{st.session_state.active_dataset}_{hash(str(viz_df.columns.tolist()))}"
                # Create PyGWalker renderer with larger height
                renderer = get_pygwalker_renderer(viz_df, walker_key)
                # Render the interactive visualization with a larger height
                renderer.explorer(default_tab="data")
            else:
                st.warning("No data available for visualization")
        except Exception as e:
            st.error(f"Error loading data for visualization: {str(e)}")
            logger.error(f"PyGWalker visualization error: {str(e)}")
            st.dataframe(viz_df, use_container_width=True)

    with tab2:        
        # Profiling Configuration Section
        st.markdown("""
            <div style="background: white; padding: 1.5rem; border-radius: 10px; 
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 2rem; border-left: 4px solid #2196F3;">
                <h3 style="margin: 0 0 1rem 0; color: #1976D2;">⚙️ Profiling Configuration</h3>
            </div>
            """, unsafe_allow_html=True)
                
        # Enhanced profiling options
        col_left, col_right = st.columns([5, 1])
                
        with col_left:
            st.markdown("**Select Analysis Mode:**")
                    
            # Custom styled radio buttons using columns
            mode_col1, mode_col2 = st.columns(2)
            
            with mode_col1:
                minimal_selected = st.button(
                    "🔬 Quick Analysis", 
                    help="Fast profiling with essential statistics and basic visualizations",
                    use_container_width=True,
                    key="minimal_mode"
                )
            
            with mode_col2:
                detailed_selected = st.button(
                    "🔍 Deep Analysis", 
                    help="Comprehensive profiling with advanced statistics, correlations, and detailed visualizations",
                    use_container_width=True,
                    key="detailed_mode"
                )
                    
            # Store the selected mode in session state
            if minimal_selected:
                st.session_state.profiling_mode = "minimal"
            elif detailed_selected:
                st.session_state.profiling_mode = "detailed"
            
            # Default to minimal if no selection
            if 'profiling_mode' not in st.session_state:
                st.session_state.profiling_mode = "minimal"
            
            # Show selected mode
            mode_display = "🔬 Quick Analysis" if st.session_state.profiling_mode == "minimal" else "🔍 Deep Analysis"
            st.info(f"**Selected Mode:** {mode_display}")

                
            # Generate Report Button with enhanced styling
            col_center = st.columns([1, 2, 1])[1]
            with col_center:
                generate_report = st.button(
                    "🚀 Generate Profiling Report",
                    help=f"Generate a comprehensive {st.session_state.profiling_mode} analysis report",
                    use_container_width=True,
                    type="primary"
                )
                
                if generate_report:
                    # Enhanced loading experience
                    progress_container = st.container()
                    
                    with progress_container:
                        # Progress indicators
                        progress_bar = st.progress(0)
                        status_text = st.empty()
                        
                        try:
                            # Step 1: Data loading
                            status_text.text("📥 Loading dataset...")
                            progress_bar.progress(20)
                            
                            df_for_profiling = lazy_dataset.get_full_data()
                            
                            # Step 2: Data preprocessing
                            status_text.text("🔧 Preprocessing data...")
                            progress_bar.progress(40)
                            
                            # Step 3: Generating profile
                            status_text.text("📊 Generating analysis report...")
                            progress_bar.progress(60)
                            
                            # Enhanced profile configuration
                            profile_config = {
                                "title": f"📈 {st.session_state.active_dataset} - Data Profile",
                                "minimal": (st.session_state.profiling_mode == "minimal"),
                                "explorative": (st.session_state.profiling_mode == "detailed"),
                                "dark_mode": False,
                                "orange_mode": False
                            }
                            
                            if st.session_state.profiling_mode == "detailed":
                                profile_config.update({
                                    "correlations": {"auto": {"calculate": True}},
                                    "missing_diagrams": {"auto": {"calculate": True}},
                                    "duplicates": {"auto": {"calculate": True}},
                                    "interactions": {"auto": {"calculate": True}}
                                })
                            
                            profile = ProfileReport(df_for_profiling, **profile_config)
                            
                            # Step 4: Rendering
                            status_text.text("🎨 Rendering report...")
                            progress_bar.progress(80)
                            
                            # Clear progress indicators
                            progress_bar.progress(100)
                            status_text.text("✅ Report generated successfully!")
                            
                            # Success message with animation
                            st.balloons()
                            
                            # Clear progress after a moment
                            time.sleep(1)
                            progress_container.empty()
                            
                            # Display the report in an enhanced container
                            st.markdown("""
                            <div style="background: white; padding: 1rem; border-radius: 10px; 
                                       box-shadow: 0 4px 20px rgba(0,0,0,0.1); margin: 2rem 0;">
                                <h3 style="margin: 0 0 1rem 0; color: #1976D2; text-align: center;">
                                    📊 Interactive Data Profile Report
                                </h3>
                            </div>
                            """, unsafe_allow_html=True)
                            
                            # Render the profile report
                            st.components.v1.html(profile.to_html(), height=1200, scrolling=True)
                            
                            # Add download option
                            st.markdown("<br>", unsafe_allow_html=True)
                            col_download = st.columns([1, 1, 1])[1]
                            with col_download:
                                if st.button("💾 Save Report as HTML", use_container_width=True):
                                    html_report = profile.to_html()
                                    st.download_button(
                                        label="📥 Download HTML Report",
                                        data=html_report,
                                        file_name=f"{st.session_state.active_dataset}_profile_report.html",
                                        mime="text/html",
                                        use_container_width=True
                                    )
                            
                        except Exception as e:
                            # Enhanced error handling
                            progress_container.empty()
                            
                            st.markdown("""
                            <div style="background: #ffebee; padding: 1.5rem; border-radius: 10px; 
                                       border-left: 4px solid #f44336; margin: 1rem 0;">
                                <h4 style="color: #c62828; margin: 0 0 0.5rem 0;">⚠️ Profiling Error</h4>
                                <p style="margin: 0; color: #666;">Unable to generate the full profiling report.</p>
                            </div>
                            """, unsafe_allow_html=True)
                            
                            st.error(f"**Error details:** {str(e)}")
                            logger.error(f"Profiling error: {str(e)}")
                            
                            # Enhanced fallback summary
                            st.markdown("""
                            <div style="background: white; padding: 1.5rem; border-radius: 10px; 
                                       box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin: 2rem 0; border-left: 4px solid #FF9800;">
                                <h3 style="margin: 0 0 1rem 0; color: #F57C00;">📋 Alternative Data Summary</h3>
                            </div>
                            """, unsafe_allow_html=True)
                            
                            col_info = lazy_dataset.get_column_info()
                            
                            # Enhanced metrics layout
                            metric_col1, metric_col2, metric_col3, metric_col4 = st.columns(4)
                            
                            with metric_col1:
                                st.metric(
                                    label="📏 Total Rows",
                                    value=f"{lazy_dataset.shape[0]:,}",
                                    help="Number of records in the dataset"
                                )
                            
                            with metric_col2:
                                st.metric(
                                    label="📋 Total Columns", 
                                    value=lazy_dataset.shape[1],
                                    help="Number of features/variables"
                                )
                            
                            with metric_col3:
                                st.metric(
                                    label="🔢 Numeric Columns",
                                    value=len(col_info['numeric_columns']),
                                    help="Columns containing numerical data"
                                )
                            
                            with metric_col4:
                                total_nulls = sum(col_info['null_counts'].values())
                                st.metric(
                                    label="❌ Missing Values",
                                    value=f"{total_nulls:,}",
                                    help="Total number of missing/null values"
                                )
                            
                            # Additional column type breakdown
                            st.markdown("<br>", unsafe_allow_html=True)
                            type_col1, type_col2, type_col3 = st.columns(3)
                            
                            with type_col1:
                                st.metric(
                                    label="📝 Text Columns",
                                    value=len(col_info['string_columns']),
                                    help="Columns containing text/categorical data"
                                )
                            
                            with type_col2:
                                st.metric(
                                    label="📅 Date Columns",
                                    value=len(col_info['date_columns']),
                                    help="Columns containing date/time data"
                                )
                            
                            with type_col3:
                                other_cols = lazy_dataset.shape[1] - len(col_info['numeric_columns']) - len(col_info['string_columns']) - len(col_info['date_columns'])
                                st.metric(
                                    label="🔧 Other Types",
                                    value=max(0, other_cols),
                                    help="Columns with other data types"
                                )
                
        with col_right:
            st.markdown("**Analysis Features:**")
            if st.session_state.profiling_mode == "minimal":
                st.markdown("""
                ✅ Basic statistics  
                ✅ Missing values  
                ✅ Data types  
                ✅ Sample data  
                ⚡ Fast execution
                """)
            else:
                st.markdown("""
                ✅ Advanced statistics  
                ✅ Correlation analysis  
                ✅ Distribution plots  
                ✅ Outlier detection  
                ✅ Duplicate analysis  
                🔍 Detailed insights
                """)
            
            st.markdown("<br>", unsafe_allow_html=True)
            

    with tab3:
        # Data Quality tab implementation using lazy dataset
        lazy_dataset = get_active_dataset()
        
        if lazy_dataset:
              # Load data for quality checks
            @st.cache_data
            def load_quality_data(dataset_name: str) -> pd.DataFrame:
                """Load data for quality checks with caching"""
                lazy_ds = st.session_state.lazy_datasets[dataset_name]
                # Always use full dataset for quality checks
                return lazy_ds.get_full_data()
            
            # Load data for quality checks
            try:
                quality_df = load_quality_data(st.session_state.active_dataset)
            except Exception as e:
                st.error(f"Error loading data for quality checks: {str(e)}")
                st.stop()
            
            # Show dataset info
            st.subheader("Dataset Information")
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Rows", f"{lazy_dataset.shape[0]:,}")
            with col2:
                st.metric("Columns", lazy_dataset.shape[1])
            with col3:
                total_nulls = quality_df.isnull().sum().sum()
                st.metric("Missing Values", f"{total_nulls:,}")
            
            # Create subtabs for different data quality operations
            quality_tab1, quality_tab2, quality_tab3 = st.tabs(["📋 Define Expectations", "🔍 Run Validations", "📊 Quality Reports"])
            
            with quality_tab1:
                st.subheader("Define Data Quality Expectations")

                # Create a new expectation suite
                st.subheader("Create Expectation Suite")

                suite_name = st.text_input("Expectation Suite Name", f"{st.session_state.active_dataset}_quality_suite")

                # Column selection for expectations
                st.subheader("Select Columns for Validation")
                # Display columns with checkboxes
                columns = quality_df.columns.tolist()
                selected_columns = st.multiselect("Select columns to validate", columns, default=columns[:5])

                if selected_columns:
                    st.subheader("Define Expectations")

                    # Create expandable sections for different types of expectations
                    with st.expander("Column Existence and Type Expectations", expanded=True):
                        st.markdown("These expectations validate the structure of your data.")

                        # Expect columns to exist
                        st.checkbox("Expect these columns to exist", value=True, key="expect_columns_to_exist")

                        # Expect column types
                        st.checkbox("Expect column types to match schema", value=True, key="expect_column_types")

                        if st.session_state.expect_column_types:
                            st.info("Column types will be inferred from the current data.")

                    with st.expander("Missing Value Expectations", expanded=True):
                        st.markdown("These expectations validate the completeness of your data.")
                        # For each selected column, add missing value expectations
                        for column in selected_columns:
                            has_nulls = quality_df[column].isnull().sum() > 0
                            st.checkbox(f"Expect '{column}' values to not be null",
                                       value=not has_nulls,
                                       key=f"not_null_{column}")

                    with st.expander("Uniqueness Expectations", expanded=True):
                        st.markdown("These expectations validate the uniqueness of values in your data.")
                        # For each selected column, add uniqueness expectations
                        for column in selected_columns:
                            unique_count = quality_df[column].nunique()
                            total_count = quality_df.shape[0] - quality_df[column].isnull().sum()
                            is_unique = unique_count == total_count
                            st.checkbox(f"Expect '{column}' values to be unique",
                                       value=is_unique,
                                       key=f"unique_{column}")

                    with st.expander("Value Range Expectations", expanded=True):
                        st.markdown("These expectations validate the range of values in your data.")

                        # For each selected numeric column, add range expectations
                        numeric_columns = [col for col in selected_columns if quality_df[col].dtype.kind in 'biufc']

                        if numeric_columns:
                            for column in numeric_columns:
                                min_val = float(quality_df[column].min())
                                max_val = float(quality_df[column].max())

                                st.subheader(f"Range for '{column}'")
                                use_range = st.checkbox(f"Expect '{column}' values to be in range",
                                                      value=True,
                                                      key=f"range_{column}")

                                if use_range:
                                    min_range, max_range = st.slider(
                                        f"Expected range for '{column}'",
                                        float(min_val - abs(min_val*0.1)),
                                        float(max_val + abs(max_val*0.1)),
                                        (float(min_val), float(max_val)),
                                        key=f"range_slider_{column}"
                                    )
                                    st.write(f"Min: {min_range}, Max: {max_range}")
                        else:
                            st.info("No numeric columns selected for range validation.")

                    # Save the expectation suite
                    if st.button("Save Expectation Suite"):
                        # Create a dictionary to store the expectations
                        expectations = {
                            "name": suite_name,
                            "dataset": st.session_state.active_dataset,
                            "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            "columns": selected_columns,
                            "expectations": []
                        }

                        # Add column existence expectations
                        if st.session_state.expect_columns_to_exist:
                            expectations["expectations"].append({
                                "type": "expect_table_columns_to_contain_set",
                                "columns": selected_columns
                            })

                        # Add column type expectations
                        if st.session_state.expect_column_types:
                            for column in selected_columns:
                                dtype = str(quality_df[column].dtype)
                                expectations["expectations"].append({
                                    "type": "expect_column_values_to_be_of_type",
                                    "column": column,
                                    "type_": dtype
                                })

                        # Add not null expectations
                        for column in selected_columns:
                            if st.session_state.get(f"not_null_{column}", False):
                                expectations["expectations"].append({
                                    "type": "expect_column_values_to_not_be_null",
                                    "column": column
                                })

                        # Add uniqueness expectations
                        for column in selected_columns:
                            if st.session_state.get(f"unique_{column}", False):
                                expectations["expectations"].append({
                                    "type": "expect_column_values_to_be_unique",
                                    "column": column
                                })

                        # Add range expectations
                        numeric_columns = [col for col in selected_columns if quality_df[col].dtype.kind in 'biufc']
                        for column in numeric_columns:
                            if st.session_state.get(f"range_{column}", False):
                                min_range = st.session_state.get(f"range_slider_{column}")[0]
                                max_range = st.session_state.get(f"range_slider_{column}")[1]

                                expectations["expectations"].append({
                                    "type": "expect_column_values_to_be_between",
                                    "column": column,
                                    "min_value": min_range,
                                    "max_value": max_range
                                })

                        # Save the expectation suite to session state
                        st.session_state.expectation_suites[suite_name] = expectations

                        st.success(f"Expectation suite '{suite_name}' saved successfully!")
            
            with quality_tab2:
                st.subheader("Run Data Validations")

                # Check if there are any expectation suites defined
                if not st.session_state.expectation_suites:
                    st.warning("No expectation suites defined. Please create an expectation suite in the 'Define Expectations' tab first.")
                else:
                    # Get expectation suites for this dataset
                    dataset_suites = {name: suite for name, suite in st.session_state.expectation_suites.items()
                                     if suite["dataset"] == st.session_state.active_dataset}

                    if not dataset_suites:
                        st.warning(f"No expectation suites defined for dataset '{st.session_state.active_dataset}'. Please create an expectation suite first.")
                    else:
                        # Select an expectation suite
                        suite_options = list(dataset_suites.keys())
                        selected_suite = st.selectbox(
                            "Select an expectation suite to run:",
                            suite_options,
                            key="selected_validation_suite"
                        )

                        if selected_suite:
                            suite = dataset_suites[selected_suite]

                            # Display suite info
                            st.subheader("Expectation Suite Information")
                            st.markdown(f"""
                            - **Suite Name**: {suite['name']}
                            - **Created**: {suite['created_at']}
                            - **Number of Expectations**: {len(suite['expectations'])}
                            - **Columns Validated**: {len(suite['columns'])}
                            """)

                            # Run validation button
                            if st.button("Run Validation"):
                                with st.spinner("Running validation..."):
                                    # Create a validation result
                                    validation_id = f"{selected_suite}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
                                    validation_results = {
                                        "id": validation_id,
                                        "suite_name": selected_suite,
                                        "dataset": st.session_state.active_dataset,
                                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                        "results": [],
                                        "summary": {
                                            "total_expectations": len(suite['expectations']),
                                            "passed_expectations": 0,
                                            "failed_expectations": 0,
                                            "success_percent": 0
                                        }
                                    }

                                    # Run each expectation
                                    for expectation in suite['expectations']:
                                        expectation_type = expectation['type']
                                        result = {
                                            "expectation_type": expectation_type,
                                            "success": False,
                                            "details": {}
                                        }

                                        # Validate based on expectation type using quality_df
                                        if expectation_type == "expect_table_columns_to_contain_set":
                                            columns_to_check = expectation['columns']
                                            missing_columns = [col for col in columns_to_check if col not in quality_df.columns]
                                            result["success"] = len(missing_columns) == 0
                                            result["details"] = {
                                                "columns_checked": columns_to_check,
                                                "missing_columns": missing_columns
                                            }

                                        elif expectation_type == "expect_column_values_to_be_of_type":
                                            column = expectation['column']
                                            expected_type = expectation['type_']
                                            actual_type = str(quality_df[column].dtype)
                                            result["success"] = expected_type == actual_type
                                            result["details"] = {
                                                "column": column,
                                                "expected_type": expected_type,
                                                "actual_type": actual_type
                                            }

                                        elif expectation_type == "expect_column_values_to_not_be_null":
                                            column = expectation['column']
                                            null_count = quality_df[column].isnull().sum()
                                            result["success"] = null_count == 0
                                            result["details"] = {
                                                "column": column,
                                                "null_count": int(null_count),
                                                "total_rows": quality_df.shape[0]
                                            }

                                        elif expectation_type == "expect_column_values_to_be_unique":
                                            column = expectation['column']
                                            unique_count = quality_df[column].nunique()
                                            total_count = quality_df.shape[0] - quality_df[column].isnull().sum()
                                            result["success"] = unique_count == total_count
                                            result["details"] = {
                                                "column": column,
                                                "unique_count": int(unique_count),
                                                "total_non_null_count": int(total_count)
                                            }
                                        
                                        elif expectation_type == "expect_column_values_to_be_between":
                                            column = expectation['column']
                                            min_value = expectation['min_value']
                                            max_value = expectation['max_value']
                                            
                                            # Check if all values are within the range (excluding nulls)
                                            col_data = quality_df[column].dropna()
                                            if len(col_data) > 0:
                                                min_actual = col_data.min()
                                                max_actual = col_data.max()
                                                result["success"] = min_actual >= min_value and max_actual <= max_value
                                            else:
                                                result["success"] = True  # No data to validate
                                            
                                            result["details"] = {
                                                "column": column,
                                                "expected_min": min_value,
                                                "expected_max": max_value,
                                                "actual_min": float(min_actual) if len(col_data) > 0 else None,
                                                "actual_max": float(max_actual) if len(col_data) > 0 else None,
                                                "non_null_count": len(col_data)
                                            }

                                        # Add result to validation results
                                        validation_results["results"].append(result)

                                        # Update summary
                                        if result["success"]:
                                            validation_results["summary"]["passed_expectations"] += 1
                                        else:
                                            validation_results["summary"]["failed_expectations"] += 1

                                    # Calculate success percentage
                                    total = validation_results["summary"]["total_expectations"]
                                    passed = validation_results["summary"]["passed_expectations"]
                                    validation_results["summary"]["success_percent"] = (passed / total * 100) if total > 0 else 0

                                    # Save validation results
                                    st.session_state.validation_results[validation_id] = validation_results

                                    # Display validation summary
                                    st.subheader("Validation Summary")

                                    # Create metrics for the summary
                                    col1, col2, col3 = st.columns(3)
                                    with col1:
                                        st.metric("Total Expectations", total)
                                    with col2:
                                        st.metric("Passed", passed)
                                    with col3:
                                        st.metric("Success Rate", f"{validation_results['summary']['success_percent']:.1f}%")

                                    # Display detailed results
                                    st.subheader("Detailed Results")

                                    # Create tabs for passed and failed expectations
                                    passed_tab, failed_tab = st.tabs(["✅ Passed", "❌ Failed"])

                                    with passed_tab:
                                        passed_results = [r for r in validation_results["results"] if r["success"]]
                                        if not passed_results:
                                            st.info("No expectations passed.")
                                        else:
                                            for i, result in enumerate(passed_results):
                                                with st.expander(f"{i+1}. {result['expectation_type']}"):
                                                    st.json(result["details"])

                                    with failed_tab:
                                        failed_results = [r for r in validation_results["results"] if not r["success"]]
                                        if not failed_results:
                                            st.success("All expectations passed!")
                                        else:
                                            for i, result in enumerate(failed_results):
                                                with st.expander(f"{i+1}. {result['expectation_type']}", expanded=True):
                                                    st.json(result["details"])
            
            with quality_tab3:
                st.subheader("Data Quality Reports")

                # Check if there are any validation results
                if not st.session_state.validation_results:
                    st.warning("No validation results available. Please run a validation in the 'Run Validations' tab first.")
                else:
                    # Filter validation results by dataset
                    filtered_results = {k: v for k, v in st.session_state.validation_results.items()
                                      if v["dataset"] == st.session_state.active_dataset}

                    if not filtered_results:
                        st.warning(f"No validation results available for dataset '{st.session_state.active_dataset}'.")
                    else:
                        # Display validation history
                        st.subheader("Validation History")

                        # Create a dataframe of validation results
                        history_data = []
                        for validation_id, validation in filtered_results.items():
                            history_data.append({
                                "Validation ID": validation_id,
                                "Suite Name": validation["suite_name"],
                                "Timestamp": validation["timestamp"],
                                "Total Expectations": validation["summary"]["total_expectations"],
                                "Passed": validation["summary"]["passed_expectations"],
                                "Failed": validation["summary"]["failed_expectations"],
                                "Success Rate": f"{validation['summary']['success_percent']:.1f}%"
                            })

                        # Convert to dataframe and display
                        if history_data:
                            history_df = pd.DataFrame(history_data)
                            st.dataframe(history_df, use_container_width=True)

                            # Select a validation to view details
                            selected_validation_id = st.selectbox(
                                "Select a validation to view details:",
                                list(filtered_results.keys()),
                                format_func=lambda x: f"{filtered_results[x]['suite_name']} ({filtered_results[x]['timestamp']})",
                                key="report_validation_select"
                            )

                            if selected_validation_id:
                                validation = filtered_results[selected_validation_id]

                                # Display validation details
                                st.subheader("Validation Details")
                                st.markdown(f"""
                                - **Dataset**: {validation['dataset']}
                                - **Suite Name**: {validation['suite_name']}
                                - **Timestamp**: {validation['timestamp']}
                                - **Total Expectations**: {validation['summary']['total_expectations']}
                                - **Passed**: {validation['summary']['passed_expectations']}
                                - **Failed**: {validation['summary']['failed_expectations']}
                                - **Success Rate**: {validation['summary']['success_percent']:.1f}%
                                """)

                                # Create visualization of validation results
                                st.subheader("Validation Results Visualization")

                                # Create a pie chart of passed/failed expectations
                                fig1 = go.Figure(data=[go.Pie(
                                    labels=['Passed', 'Failed'],
                                    values=[validation['summary']['passed_expectations'], validation['summary']['failed_expectations']],
                                    hole=.3,
                                    marker_colors=['#4CAF50', '#F44336']
                                )])
                                fig1.update_layout(title_text="Passed vs Failed Expectations")
                                st.plotly_chart(fig1, use_container_width=True)

                                # Create a bar chart of expectation types
                                expectation_types = {}
                                for result in validation['results']:
                                    exp_type = result['expectation_type']
                                    if exp_type not in expectation_types:
                                        expectation_types[exp_type] = {'passed': 0, 'failed': 0}

                                    if result["success"]:
                                        expectation_types[exp_type]['passed'] += 1
                                    else:
                                        expectation_types[exp_type]['failed'] += 1

                                # Prepare data for the bar chart
                                exp_names = list(expectation_types.keys())
                                passed_counts = [expectation_types[exp]['passed'] for exp in exp_names]
                                failed_counts = [expectation_types[exp]['failed'] for exp in exp_names]

                                # Create the bar chart
                                fig2 = go.Figure(data=[
                                    go.Bar(name='Passed', x=exp_names, y=passed_counts, marker_color='#4CAF50'),
                                    go.Bar(name='Failed', x=exp_names, y=failed_counts, marker_color='#F44336')
                                ])
                                fig2.update_layout(
                                    title_text='Results by Expectation Type',
                                    xaxis_title='Expectation Type',
                                    yaxis_title='Count',
                                    barmode='stack'
                                )
                                st.plotly_chart(fig2, use_container_width=True)

                                # Export options
                                st.subheader("Export Options")

                                # Export as JSON
                                if st.button("Export Validation Results as JSON"):
                                    # Create a deep copy of validation results to avoid modifying the original
                                    validation_copy = copy.deepcopy(validation)

                                    # Helper function to convert NumPy types to Python native types
                                    def convert_numpy_types(obj):
                                        if isinstance(obj, dict):
                                            return {k: convert_numpy_types(v) for k, v in obj.items()}
                                        elif isinstance(obj, list):
                                            return [convert_numpy_types(item) for item in obj]
                                        elif isinstance(obj, np.integer):
                                            return int(obj)
                                        elif isinstance(obj, np.floating):
                                            return float(obj)
                                        elif isinstance(obj, np.ndarray):
                                            return convert_numpy_types(obj.tolist())
                                        elif isinstance(obj, np.bool_):
                                            return bool(obj)
                                        else:
                                            return obj

                                    # Convert all NumPy types to Python native types
                                    validation_json_safe = convert_numpy_types(validation_copy)

                                    # Convert validation results to JSON
                                    json_results = json.dumps(validation_json_safe, indent=2)

                                    # Create a download link
                                    st.download_button(
                                        label="Download JSON",
                                        data=json_results,
                                        file_name=f"validation_{selected_validation_id}.json",
                                        mime="application/json"
                                    )

                                # Generate HTML report
                                if st.button("Generate HTML Report"):
                                    # Create an HTML report
                                    html_report = f"""
                                    <!DOCTYPE html>
                                    <html>
                                    <head>
                                        <title>Data Quality Report - {validation['dataset']}</title>
                                        <style>
                                            body {{ font-family: Arial, sans-serif; margin: 20px; }}
                                            h1, h2, h3 {{ color: #2C3E50; }}
                                            .summary {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
                                            .passed {{ color: #4CAF50; }}
                                            .failed {{ color: #F44336; }}
                                            table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
                                            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                                            th {{ background-color: #f2f2f2; }}
                                            tr:nth-child(even) {{ background-color: #f9f9f9; }}
                                        </style>
                                    </head>
                                    <body>
                                        <h1>Data Quality Report</h1>
                                        <div class="summary">
                                            <h2>Validation Summary</h2>
                                            <p><strong>Dataset:</strong> {validation['dataset']}</p>
                                            <p><strong>Suite Name:</strong> {validation['suite_name']}</p>
                                            <p><strong>Timestamp:</strong> {validation['timestamp']}</p>
                                            <p><strong>Total Expectations:</strong> {validation['summary']['total_expectations']}</p>
                                            <p><strong>Passed:</strong> <span class="passed">{validation['summary']['passed_expectations']}</span></p>
                                            <p><strong>Failed:</strong> <span class="failed">{validation['summary']['failed_expectations']}</span></p>
                                            <p><strong>Success Rate:</strong> {validation['summary']['success_percent']:.1f}%</p>
                                        </div>

                                        <h2>Detailed Results</h2>
                                        <table>
                                            <tr>
                                                <th>#</th>
                                                <th>Expectation Type</th>
                                                <th>Status</th>
                                                <th>Details</th>
                                            </tr>
                                    """

                                    # Add rows for each expectation result
                                    for i, result in enumerate(validation['results']):
                                        status = '<span class="passed">Passed</span>' if result['success'] else '<span class="failed">Failed</span>'
                                        # Convert NumPy types to Python native types before JSON serialization
                                        json_safe_details = convert_numpy_types(result['details'])
                                        details = json.dumps(json_safe_details, indent=2).replace('\n', '<br>').replace(' ', '&nbsp;')
                                        html_report += f"""
                                            <tr>
                                                <td>{i+1}</td>
                                                <td>{result['expectation_type']}</td>
                                                <td>{status}</td>
                                                <td><pre>{details}</pre></td>
                                            </tr>
                                        """

                                    # Close the HTML
                                    html_report += """
                                        </table>
                                        <p><em>Report generated by Data Quality Check application</em></p>
                                    </body>
                                    </html>
                                    """

                                    # Create a download link
                                    st.download_button(
                                        label="Download HTML Report",
                                        data=html_report,
                                        file_name=f"quality_report_{selected_validation_id}.html",
                                        mime="text/html"
                                    )

    with tab4:
        st.subheader("Compare Datasets")
        
        # Only show comparison UI if we have at least 2 datasets
        if len(st.session_state.lazy_datasets) < 2:
            st.warning("You need at least two datasets to perform a comparison. Please upload another dataset.")
        else:
            # Dataset selection
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("Dataset 1")
                dataset1 = st.selectbox(
                    "Select first dataset:",
                    options=list(st.session_state.lazy_datasets.keys()),
                    key="compare_dataset1"
                )
            
            with col2:
                st.subheader("Dataset 2")
                # Filter out the first selected dataset
                remaining_datasets = [ds for ds in st.session_state.lazy_datasets.keys() if ds != dataset1]
                dataset2 = st.selectbox(
                    "Select second dataset",
                    options=remaining_datasets,
                    key="compare_dataset2"
                )
            
            # Join column selection
            if dataset1 and dataset2:
                # Get column lists for both datasets
                # Use get_column_info() instead of get_column_names()
                df1_cols = list(st.session_state.lazy_datasets[dataset1].get_column_info()['dtypes'].keys())
                df2_cols = list(st.session_state.lazy_datasets[dataset2].get_column_info()['dtypes'].keys())
                
                # Find common columns as potential join keys
                common_cols = list(set(df1_cols).intersection(set(df2_cols)))
                
                # Let user select join columns
                join_cols = st.multiselect(
                    "Select columns to join on:",
                    options=common_cols,
                    default=common_cols[:1] if common_cols else []
                )

                # Let user select columns to ignore
                ignore_cols = st.multiselect(
                    "Select columns to ignore in comparison (optional):",
                    options=common_cols,
                    default=[]
                )
                
                if join_cols:
                    if st.button("Run Comparison", type="primary"):
                        with st.spinner("Comparing datasets..."):
                            try:
                                # Load the full datasets
                                df1 = st.session_state.lazy_datasets[dataset1].get_full_data()
                                df2 = st.session_state.lazy_datasets[dataset2].get_full_data()
                                
                                # Filter out ignored columns
                                if ignore_cols:
                                    df1 = df1.drop(columns=[col for col in ignore_cols if col in df1.columns])
                                    df2 = df2.drop(columns=[col for col in ignore_cols if col in df2.columns])
                                
                                # Initialize the comparison
                                compare = datacompy.Compare(
                                    df1,
                                    df2,
                                    join_columns=join_cols,
                                )
                                
                                # Generate custom report
                                summary, only_df1, only_df2, mismatch_df, common_cols, only_df1_cols, only_df2_cols = generate_custom_report(compare, df1, df2)
                                
                                # Display results in tabs
                                comp_tab1, comp_tab2, comp_tab3 = st.tabs(["Summary", "Mismatched Rows", "Unique Rows"])
                                
                                with comp_tab1:
                                    st.header("Summary")
                                    
                                    # Create metrics for key comparison stats
                                    col1, col2, col3 = st.columns(3)
                                    with col1:
                                        st.metric("Common Columns", summary['common_columns'])
                                        if summary['columns_only_df1']:
                                            st.markdown(f"**Columns only in {dataset1}:** {', '.join(summary['columns_only_df1'])}")
                                        if summary['columns_only_df2']:
                                            st.markdown(f"**Columns only in {dataset2}:** {', '.join(summary['columns_only_df2'])}")
                                    
                                    with col2:
                                        st.metric("Matching Rows", summary['matching_rows'])
                                        st.metric("Mismatched Rows", summary['mismatched_rows'])
                                    
                                    with col3:
                                        st.metric(f"Rows only in {dataset1}", summary['rows_only_df1'])
                                        st.metric(f"Rows only in {dataset2}", summary['rows_only_df2'])
                                    
                                    # Display column match percentages
                                    if compare and hasattr(compare, 'column_stats'):
                                        st.subheader("Column Match Percentages")
                                        match_data = []
                                        
                                        # Get common columns between datasets (excluding join columns from comparison)
                                        comparison_cols = [col for col in common_cols if col not in join_cols]
                                        
                                        for item in compare.column_stats:
                                            match_data.append({"Column": item['column'], "Match %": 100*item['match_cnt']/summary['matching_rows']})
                                        
                                        if match_data:
                                            match_df = pl.DataFrame(match_data)
                                            match_df = match_df.sort("Match %")
                                            
                                            # Create a bar chart of match percentages
                                            fig = px.bar(
                                                match_df.to_pandas(), 
                                                x="Column", 
                                                y="Match %",
                                                color="Match %",
                                                color_continuous_scale=["red", "yellow", "green"],
                                                range_color=[0, 100],
                                            )
                                            fig.update_layout(height=500)
                                            st.plotly_chart(fig, use_container_width=True)
                                        else:
                                            st.info("No comparable columns found (excluding join columns).")

                                with comp_tab2:
                                    # Display mismatched rows without pagination
                                    st.subheader("Mismatched Rows")
                                    
                                    if mismatch_df.height > 0:
                                        # Display with highlighting but without pagination
                                        if "_df1" in "".join(mismatch_df.columns):
                                            try:
                                                # Convert to pandas with styling
                                                styled_df = highlight_polars_diff(mismatch_df)
                                                # Display the styled dataframe
                                                st.dataframe(styled_df, use_container_width=True)
                                            except Exception as e:
                                                st.error(f"Error applying styling: {str(e)}")
                                                # Fallback to unstyled display
                                                st.dataframe(mismatch_df, use_container_width=True)
                                        else:
                                            # Regular display without styling
                                            st.dataframe(mismatch_df, use_container_width=True)                                       
                                    else:
                                        st.success("No mismatched rows found!")

                                with comp_tab3:
                                    # Display unique rows with Polars
                                    st.subheader(f"Rows Unique to {dataset1}")
                                    st.dataframe(only_df1, use_container_width=True, hide_index=True)
                                    st.subheader(f"Rows Unique to {dataset2}")
                                    st.dataframe(only_df2, use_container_width=True, hide_index=True)
                                
                            except Exception as e:
                                st.error(f"Error comparing datasets: {str(e)}")
                                logger.error(f"Dataset comparison error: {str(e)}")
                                logger.error(traceback.format_exc())
                else:
                    st.warning("Please select at least one column to join on.")

st.markdown("""
<style>
[data-testid="stAppViewContainer"] > .main,
[data-testid="stMainBlock"],
[data-testid="stMainBlockContainer"],
.block-container {
    padding-top: 0 !important;
    margin-top: 0 !important;
}
</style>
""", unsafe_allow_html=True)
